import { useRef, useEffect, useState, useCallback } from "react";
import { Types, cache } from "@cornerstonejs/core";
import { ToolGroupManager } from "@cornerstonejs/tools";
import { Canvas } from "fabric";
import {
  initializeCornerstone,
  createRenderingEngine,
  setup3dViewport,
  setup2dViewport,
  loadDicomVolume,
  loadDicomStack,
  adjustVolumeShift,
  setupViewer,
  volumeViewerConfig,
  volume2dModeConfig,
} from "@/lib/dicom";
import {
  createCanvas,
  loadImageToCanvas,
  getCurrentImage,
  setCurrentImage,
} from "@/lib/fabric";
import { FabricToolbar } from "@/components/toolbars/fabric";
import { saveVolumeConfig } from "@/shared/api";
import { dicomConstants } from "@/shared/constants";
import { DicomVolumeViewerProps } from "@/shared/types";

const { renderingEngineId, viewportId, toolGroupId } = dicomConstants;

export default function VolumeViewer({ data }: DicomVolumeViewerProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const viewportRef = useRef<
    Types.IStackViewport | Types.IVolumeViewport | null
  >(null);
  const dicomFilesRef = useRef<string[]>([]);
  const fabricCanvasRef = useRef<Canvas | null>(null);
  const fabricCanvasElementRef = useRef<HTMLCanvasElement | null>(null);

  const [isInitialized, setIsInitialized] = useState(false);
  const [is3D, setIs3D] = useState(true);
  const [shift, setShift] = useState(data.viewer.configs.shift);

  // Initialize Fabric.js overlay canvas (similar to StackViewer)
  const initializeFabricOverlay = async (dicomElement: HTMLDivElement) => {
    // Create a canvas element overlay
    const canvasElement = document.createElement("canvas");
    canvasElement.id = `fabric-overlay-${viewportId}`;
    canvasElement.style.position = "absolute";
    canvasElement.style.top = "0";
    canvasElement.style.left = "0";
    canvasElement.style.pointerEvents = "none";
    canvasElement.style.zIndex = "10";

    // Set canvas size to match DICOM element
    const rect = dicomElement.getBoundingClientRect();
    canvasElement.width = rect.width;
    canvasElement.height = rect.height;

    // Add canvas to DICOM element
    dicomElement.style.position = "relative";
    dicomElement.appendChild(canvasElement);

    // Create Fabric.js canvas
    const fabricCanvas = createCanvas(canvasElement);
    fabricCanvasRef.current = fabricCanvas;
    fabricCanvasElementRef.current = canvasElement;

    // Load a transparent image to enable Fabric.js effects
    const transparentImageData =
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
    await loadImageToCanvas(fabricCanvas, transparentImageData);

    const currentImg = getCurrentImage();
    if (currentImg) {
      setCurrentImage(currentImg);
      currentImg.set({
        left: fabricCanvas.getWidth() / 2,
        top: fabricCanvas.getHeight() / 2,
        scaleX: fabricCanvas.getWidth(),
        scaleY: fabricCanvas.getHeight(),
        originX: "center",
        originY: "center",
      });
      fabricCanvas.renderAll();
    }
  };

  const getCanvas = () => fabricCanvasRef.current;

  const handleShiftChange = (value: number) => {
    if (!viewportRef.current || !is3D) return;
    setShift(value);
    adjustVolumeShift(viewportRef.current as Types.IVolumeViewport, value);
  };

  const handleSave = async () => {
    await saveVolumeConfig(data.id, { shift });
  };

  const switchTo3D = useCallback(async () => {
    if (!elementRef.current || !renderingEngineRef.current) return;

    ToolGroupManager.destroyToolGroup(toolGroupId);

    const viewport = setup3dViewport(
      renderingEngineRef.current,
      elementRef.current,
      viewportId
    );
    viewportRef.current = viewport;

    setupViewer(toolGroupId, viewportId, renderingEngineId, volumeViewerConfig);

    await loadDicomVolume(viewport, dicomFilesRef.current);
    adjustVolumeShift(viewport, shift);
    setIs3D(true);
  }, [shift]);

  const switchTo2D = useCallback(async () => {
    if (!elementRef.current || !renderingEngineRef.current) return;

    ToolGroupManager.destroyToolGroup(toolGroupId);

    const viewport = setup2dViewport(
      renderingEngineRef.current,
      elementRef.current,
      viewportId
    );
    viewportRef.current = viewport;

    setupViewer(toolGroupId, viewportId, renderingEngineId, volume2dModeConfig);

    await loadDicomStack(viewport, dicomFilesRef.current);
    setIs3D(false);
  }, []);

  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);

    return () => {
      ToolGroupManager.destroyToolGroup(toolGroupId);
      renderingEngineRef.current?.destroy();
      cache.purgeCache();

      // Cleanup Fabric.js canvas
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
        fabricCanvasRef.current = null;
      }
      if (fabricCanvasElementRef.current) {
        fabricCanvasElementRef.current.remove();
        fabricCanvasElementRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (!isInitialized || !elementRef.current) return;

    const initializeViewer = async () => {
      cache.purgeCache();

      const renderingEngine = createRenderingEngine(renderingEngineId);
      renderingEngineRef.current = renderingEngine;

      const element = elementRef.current;
      if (!element) return;

      const viewport = setup3dViewport(renderingEngine, element, viewportId);
      viewportRef.current = viewport;

      setupViewer(
        toolGroupId,
        viewportId,
        renderingEngineId,
        volumeViewerConfig
      );

      dicomFilesRef.current = data.viewer.imageUrl;
      await loadDicomVolume(viewport, data.viewer.imageUrl);
      setIs3D(true);
      adjustVolumeShift(viewport, shift);

      // Initialize Fabric.js overlay
      await initializeFabricOverlay(element);
    };

    initializeViewer();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized, data]);

  return (
    <div className="volume-viewer">
      <div ref={elementRef} style={{ width: "100%", height: "100%" }} />

      {is3D && (
        <div className="controls">
          <div className="control-group">
            <label>Shift:</label>
            <input
              type="range"
              min="0"
              max="3000"
              step="100"
              value={shift}
              onChange={(e) => handleShiftChange(parseInt(e.target.value))}
            />
            <span>{shift}</span>
          </div>

          <button className="save-button" onClick={handleSave}>
            Save Settings
          </button>
        </div>
      )}

      <div className="mode-switcher">
        <span
          className={`mode-switcher-item ${is3D ? "active" : "inactive"}`}
          onClick={switchTo3D}
        >
          3D
        </span>
        <span className="mode-switcher-separator">|</span>
        <span
          className={`mode-switcher-item ${!is3D ? "active" : "inactive"}`}
          onClick={switchTo2D}
        >
          2D
        </span>
      </div>
    </div>
  );
}
