import { useEffect, useState } from "react";
import type { FabricToolbarProps } from "@/shared/types";

export default function FabricToolbar({
  initialValues,
  onSave,
  cssFilterMode,
}: FabricToolbarProps) {
  const [brightness, setBrightness] = useState(initialValues.brightness || 1);
  const [contrast, setContrast] = useState(initialValues.contrast || 1);
  const [saturation, setSaturation] = useState(initialValues.saturation || 1);
  const [rotation, setRotation] = useState(initialValues.rotation || 0);

  useEffect(() => {
    setBrightness(initialValues.brightness || 1);
    setContrast(initialValues.contrast || 1);
    setSaturation(initialValues.saturation || 1);
    setRotation(initialValues.rotation || 0);
  }, [initialValues]);

  const handleBrightnessChange = (value: number) => {
    setBrightness(value);

    const element = cssFilterMode.targetElement();
    if (element) {
      const currentFilter = element.style.filter;
      const newFilter =
        currentFilter.replace(/brightness\([^)]*\)/, "") +
        ` brightness(${value})`;
      element.style.filter = newFilter.trim();
    }
  };

  const handleContrastChange = (value: number) => {
    setContrast(value);

    const element = cssFilterMode.targetElement();
    if (element) {
      const currentFilter = element.style.filter;
      const newFilter =
        currentFilter.replace(/contrast\([^)]*\)/, "") + ` contrast(${value})`;
      element.style.filter = newFilter.trim();
    }
  };

  const handleSaturationChange = (value: number) => {
    setSaturation(value);

    const element = cssFilterMode.targetElement();
    if (element) {
      const currentFilter = element.style.filter;
      const newFilter =
        currentFilter.replace(/saturate\([^)]*\)/, "") + ` saturate(${value})`;
      element.style.filter = newFilter.trim();
    }
  };

  const handleRotationChange = (value: number) => {
    setRotation(value);

    const element = cssFilterMode.targetElement();
    if (element) {
      element.style.transform = `rotate(${value}deg)`;
    }
  };

  const handleSave = () => {
    if (onSave) {
      onSave({
        brightness,
        contrast,
        saturation,
        rotation,
      });
    }
  };

  return (
    <div className="fabric-toolbar">
      <div className="toolbar-header">
        <h4>Fabric Controls</h4>
      </div>

      <div className="control-group">
        <label>Brightness:</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={brightness}
          onChange={(e) => handleBrightnessChange(parseFloat(e.target.value))}
        />
        <span>{brightness.toFixed(1)}</span>
      </div>

      <div className="control-group">
        <label>Contrast:</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={contrast}
          onChange={(e) => handleContrastChange(parseFloat(e.target.value))}
        />
        <span>{contrast.toFixed(1)}</span>
      </div>

      <div className="control-group">
        <label>Saturation:</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={saturation}
          onChange={(e) => handleSaturationChange(parseFloat(e.target.value))}
        />
        <span>{saturation.toFixed(1)}</span>
      </div>

      <div className="control-group">
        <label>Rotation:</label>
        <input
          type="range"
          min="0"
          max="360"
          step="15"
          value={rotation}
          onChange={(e) => handleRotationChange(parseFloat(e.target.value))}
        />
        <span>{rotation}°</span>
      </div>

      {onSave && (
        <button className="save-button" onClick={handleSave}>
          Save Settings
        </button>
      )}
    </div>
  );
}
