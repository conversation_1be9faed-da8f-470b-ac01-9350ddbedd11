import { useRef, useEffect } from "react";
import { Canvas } from "fabric";
import {
  createCanvas,
  loadImageToCanvas,
  getCurrentImage,
  adjustImageBrightness,
  adjustImageContrast,
  adjustImageSaturation,
  rotateImage,
  setCurrentImage,
} from "@/lib/fabric";

import { saveImageConfig } from "@/shared/api";
import { MedicalImageViewerProps } from "@/shared/types";
import FabricToolbar from "@/components/toolbars/FabricToolbar";

export default function ImageViewer({ data }: MedicalImageViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<Canvas | null>(null);

  useEffect(() => {
    if (!canvasRef.current || fabricCanvasRef.current) return;

    const initializeCanvas = async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));

      if (!canvasRef.current || fabricCanvasRef.current) return;

      const canvas = createCanvas(canvasRef.current);
      fabricCanvasRef.current = canvas;

      const imageUrl = data.viewer.imageUrl;
      await loadImageToCanvas(canvas, imageUrl);

      const currentImg = getCurrentImage();
      if (currentImg) {
        setCurrentImage(currentImg);

        adjustImageBrightness(canvas, data.viewer.configs.brightness);
        adjustImageContrast(canvas, data.viewer.configs.contrast);
        adjustImageSaturation(canvas, data.viewer.configs.saturation);
        rotateImage(canvas, data.viewer.configs.rotation);
      }
    };

    initializeCanvas();

    return () => {
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
        fabricCanvasRef.current = null;
      }
    };
  }, [data]);

  const getCanvas = () => fabricCanvasRef.current;

  const handleSave = async (values: {
    brightness: number;
    contrast: number;
    saturation: number;
    rotation: number;
  }) => {
    await saveImageConfig(data.id, values);
  };

  return (
    <div className="image-viewer">
      <canvas ref={canvasRef} id="image-canvas" />

      <div className="controls">
        <FabricToolbar
          getCanvas={getCanvas}
          initialValues={{
            brightness: data.viewer.configs.brightness,
            contrast: data.viewer.configs.contrast,
            saturation: data.viewer.configs.saturation,
            rotation: data.viewer.configs.rotation,
          }}
          onSave={handleSave}
        />
      </div>
    </div>
  );
}
