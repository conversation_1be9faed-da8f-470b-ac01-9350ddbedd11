import { Canvas, FabricImage } from "fabric";

export function createCanvas(canvasElement: HTMLCanvasElement): Canvas {
  const container = canvasElement.parentElement;
  const width = container?.clientWidth || window.innerWidth;
  const height = container?.clientHeight || window.innerHeight;

  const finalWidth = Math.max(width, 100);
  const finalHeight = Math.max(height, 100);

  const canvas = new Canvas(canvasElement, {
    width: finalWidth,
    height: finalHeight,
    backgroundColor: "#2a2a2a",
  });

  return canvas;
}

export async function loadImageToCanvas(
  canvas: Canvas,
  imageUrl: string
): Promise<void> {
  const img = await FabricImage.fromURL(imageUrl, {
    crossOrigin: "anonymous",
  });
  canvas.clear();
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();
  const imgWidth = img.width || 1;
  const imgHeight = img.height || 1;
  const scale = Math.min(canvasWidth / imgWidth, canvasHeight / imgHeight);

  img.scale(scale);
  img.set({
    left: canvasWidth / 2,
    top: canvasHeight / 2,
    originX: "center",
    originY: "center",
  });

  currentImageRef = img;
  canvas.add(img);
  canvas.setActiveObject(img);
  canvas.renderAll();
}

let currentImageRef: FabricImage | null = null;

export function getCurrentImage(): FabricImage | null {
  return currentImageRef;
}

export function resizeCanvas(canvas: Canvas) {
  const canvasElement = canvas.getElement();
  const container = canvasElement.parentElement;

  if (container) {
    const width = container.clientWidth;
    const height = container.clientHeight;

    canvas.setDimensions({ width, height });
    canvas.renderAll();
  }
}
