import { Types } from "@cornerstonejs/core";

let cornerstoneCanvas: HTMLCanvasElement | null = null;

export function initializeDicomFabricCanvas(
  viewport: Types.IStackViewport
): boolean {
  const canvas = viewport.getCanvas();
  if (!canvas) return false;

  cornerstoneCanvas = canvas;
  return true;
}

export function adjustDicomImageBrightness(_canvas: unknown, value: number) {
  if (!cornerstoneCanvas) return;

  const brightnessValue = 1 + value / 127;
  cornerstoneCanvas.style.filter = `brightness(${brightnessValue})`;
}
