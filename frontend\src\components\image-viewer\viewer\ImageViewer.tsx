import { useRef, useEffect, useState } from "react";
import { Canvas } from "fabric";
import {
  createCanvas,
  loadImageToCanvas,
  getCurrentImage,
} from "@/components/fabric-toolbar/core/fabricManager";
import {
  adjustImageBrightness,
  adjustImageContrast,
  adjustImageSaturation,
  rotateImage,
  setCurrentImage,
} from "@/components/fabric-toolbar/config/fabricControls";
import { saveImageConfig } from "@/shared/api";
import { MedicalImageViewerProps } from "@/shared/types";

export default function ImageViewer({ data }: MedicalImageViewerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<Canvas | null>(null);
  const [brightness, setBrightness] = useState(data.viewer.configs.brightness);
  const [contrast, setContrast] = useState(data.viewer.configs.contrast);
  const [saturation, setSaturation] = useState(data.viewer.configs.saturation);
  const [rotation, setRotation] = useState(data.viewer.configs.rotation);

  useEffect(() => {
    if (!canvasRef.current || fabricCanvasRef.current) return;

    const initializeCanvas = async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));

      if (!canvasRef.current || fabricCanvasRef.current) return;

      const canvas = createCanvas(canvasRef.current);
      fabricCanvasRef.current = canvas;

      const imageUrl = data.viewer.imageUrl;
      await loadImageToCanvas(canvas, imageUrl);

      const currentImg = getCurrentImage();
      if (currentImg) {
        setCurrentImage(currentImg);

        adjustImageBrightness(canvas, brightness);
        adjustImageContrast(canvas, contrast);
        adjustImageSaturation(canvas, saturation);
        rotateImage(canvas, rotation);
      }
    };

    initializeCanvas();

    return () => {
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
        fabricCanvasRef.current = null;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  useEffect(() => {
    if (fabricCanvasRef.current) {
      adjustImageBrightness(fabricCanvasRef.current, brightness);
    }
  }, [brightness]);

  useEffect(() => {
    if (fabricCanvasRef.current) {
      adjustImageContrast(fabricCanvasRef.current, contrast);
    }
  }, [contrast]);

  useEffect(() => {
    if (fabricCanvasRef.current) {
      adjustImageSaturation(fabricCanvasRef.current, saturation);
    }
  }, [saturation]);

  useEffect(() => {
    if (fabricCanvasRef.current) {
      rotateImage(fabricCanvasRef.current, rotation);
    }
  }, [rotation]);

  const handleBrightnessChange = (value: number) => {
    if (!fabricCanvasRef.current) return;
    setBrightness(value);
    adjustImageBrightness(fabricCanvasRef.current, value);
  };

  const handleContrastChange = (value: number) => {
    if (!fabricCanvasRef.current) return;
    setContrast(value);
    adjustImageContrast(fabricCanvasRef.current, value);
  };

  const handleSaturationChange = (value: number) => {
    if (!fabricCanvasRef.current) return;
    setSaturation(value);
    adjustImageSaturation(fabricCanvasRef.current, value);
  };

  const handleRotationChange = (value: number) => {
    if (!fabricCanvasRef.current) return;
    setRotation(value);
    rotateImage(fabricCanvasRef.current, value);
  };

  const handleSave = async () => {
    await saveImageConfig(data.id, {
      brightness,
      contrast,
      saturation,
      rotation,
    });
  };

  return (
    <div className="image-viewer">
      <canvas ref={canvasRef} id="image-canvas" />

      <div className="controls">
        <div className="control-group">
          <label>Brightness:</label>
          <input
            type="range"
            min="0"
            max="2"
            step="0.1"
            value={brightness}
            onChange={(e) => handleBrightnessChange(parseFloat(e.target.value))}
          />
          <span>{brightness.toFixed(1)}</span>
        </div>

        <div className="control-group">
          <label>Contrast:</label>
          <input
            type="range"
            min="0"
            max="2"
            step="0.1"
            value={contrast}
            onChange={(e) => handleContrastChange(parseFloat(e.target.value))}
          />
          <span>{contrast.toFixed(1)}</span>
        </div>

        <div className="control-group">
          <label>Saturation:</label>
          <input
            type="range"
            min="0"
            max="2"
            step="0.1"
            value={saturation}
            onChange={(e) => handleSaturationChange(parseFloat(e.target.value))}
          />
          <span>{saturation.toFixed(1)}</span>
        </div>

        <div className="control-group">
          <label>Rotation:</label>
          <input
            type="range"
            min="0"
            max="360"
            step="15"
            value={rotation}
            onChange={(e) => handleRotationChange(parseFloat(e.target.value))}
          />
          <span>{rotation}°</span>
        </div>

        <button className="save-button" onClick={handleSave}>
          Save Settings
        </button>
      </div>
    </div>
  );
}
