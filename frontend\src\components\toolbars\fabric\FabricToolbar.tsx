import { useEffect, useState } from "react";
import { Canvas } from "fabric";
import {
  adjustImageBrightness,
  adjustImageContrast,
  adjustImageSaturation,
  rotateImage,
} from "@/lib/fabric";



export default function FabricToolbar({
  getCanvas,
  initialValues,
  onSave,
}: FabricToolbarProps) {
  // State for Fabric.js controls
  const [brightness, setBrightness] = useState(initialValues.brightness || 1);
  const [contrast, setContrast] = useState(initialValues.contrast || 1);
  const [saturation, setSaturation] = useState(initialValues.saturation || 1);
  const [rotation, setRotation] = useState(initialValues.rotation || 0);

  // Update state when initial values change
  useEffect(() => {
    setBrightness(initialValues.brightness || 1);
    setContrast(initialValues.contrast || 1);
    setSaturation(initialValues.saturation || 1);
    setRotation(initialValues.rotation || 0);
  }, [initialValues]);

  // Fabric.js image controls
  const handleBrightnessChange = (value: number) => {
    const canvas = getCanvas();
    if (!canvas) return;
    setBrightness(value);
    adjustImageBrightness(canvas, value);
  };

  const handleContrastChange = (value: number) => {
    const canvas = getCanvas();
    if (!canvas) return;
    setContrast(value);
    adjustImageContrast(canvas, value);
  };

  const handleSaturationChange = (value: number) => {
    const canvas = getCanvas();
    if (!canvas) return;
    setSaturation(value);
    adjustImageSaturation(canvas, value);
  };

  const handleRotationChange = (value: number) => {
    const canvas = getCanvas();
    if (!canvas) return;
    setRotation(value);
    rotateImage(canvas, value);
  };

  // Save functionality
  const handleSave = () => {
    if (onSave) {
      onSave({
        brightness,
        contrast,
        saturation,
        rotation,
      });
    }
  };

  return (
    <div className="fabric-toolbar">
      {/* Header */}
      <div className="toolbar-header">
        <h4>Fabric Controls</h4>
      </div>

      {/* Standard Fabric.js Controls */}
      <div className="control-group">
        <label>Brightness:</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={brightness}
          onChange={(e) => handleBrightnessChange(parseFloat(e.target.value))}
        />
        <span>{brightness.toFixed(1)}</span>
      </div>

      <div className="control-group">
        <label>Contrast:</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={contrast}
          onChange={(e) => handleContrastChange(parseFloat(e.target.value))}
        />
        <span>{contrast.toFixed(1)}</span>
      </div>

      <div className="control-group">
        <label>Saturation:</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={saturation}
          onChange={(e) => handleSaturationChange(parseFloat(e.target.value))}
        />
        <span>{saturation.toFixed(1)}</span>
      </div>

      <div className="control-group">
        <label>Rotation:</label>
        <input
          type="range"
          min="0"
          max="360"
          step="15"
          value={rotation}
          onChange={(e) => handleRotationChange(parseFloat(e.target.value))}
        />
        <span>{rotation}°</span>
      </div>

      {/* Save Button */}
      {onSave && (
        <button className="save-button" onClick={handleSave}>
          Save Settings
        </button>
      )}
    </div>
  );
}
