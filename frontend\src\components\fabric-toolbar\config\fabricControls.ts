import { Canvas, FabricImage, filters } from "fabric";

let currentImage: FabricImage | null = null;

export function setCurrentImage(image: FabricImage) {
  currentImage = image;
}

function applyFilter(
  canvas: Canvas,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  FilterClass: any,
  property: string,
  value: number
) {
  if (!currentImage) return;

  currentImage.filters = currentImage.filters || [];

  const existingIndex = currentImage.filters.findIndex(
    (filter) => filter instanceof FilterClass
  );

  const filter = new FilterClass({ [property]: value - 1 });

  if (existingIndex >= 0) {
    currentImage.filters[existingIndex] = filter;
  } else {
    currentImage.filters.push(filter);
  }

  currentImage.applyFilters();
  canvas.renderAll();
}

export function adjustImageBrightness(canvas: Canvas, value: number) {
  applyFilter(canvas, filters.Brightness, "brightness", value);
}

export function adjustImageContrast(canvas: Canvas, value: number) {
  applyFilter(canvas, filters.Contrast, "contrast", value);
}

export function adjustImageSaturation(canvas: Canvas, value: number) {
  applyFilter(canvas, filters.Saturation, "saturation", value);
}

export function rotateImage(canvas: Canvas, angle: number) {
  if (!currentImage) return;
  currentImage.rotate(angle);
  canvas.renderAll();
}
