import { Canvas } from "fabric";

// API Response Types
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
}

// Medical Viewer Data Types
export interface DicomStackData {
  id: string;
  viewer: {
    imageUrl: string;
    configs: {
      contrast: number;
      brightness: number;
      annotations?: AnnotationData[];
    };
  };
}

export interface DicomVolumeData {
  id: string;
  viewer: {
    imageUrl: string[];
    configs: {
      shift: number;
    };
  };
}

export interface MedicalImageData {
  id: string;
  viewer: {
    imageUrl: string;
    configs: {
      brightness: number;
      contrast: number;
      saturation: number;
      rotation: number;
    };
  };
}

// Medical Viewer Props Types
export interface DicomStackViewerProps {
  data: DicomStackData;
}

export interface DicomVolumeViewerProps {
  data: DicomVolumeData;
}

export interface MedicalImageViewerProps {
  data: MedicalImageData;
}

// Medical Viewer Config Types
export interface DicomStackConfig {
  contrast: number;
  brightness: number;
  annotations?: AnnotationData[];
}

export interface DicomVolumeConfig {
  shift: number;
}

export interface MedicalImageConfig {
  brightness: number;
  contrast: number;
  saturation: number;
  rotation: number;
}

// File and Patient Types
export interface MedicalFileItem {
  id: string;
  name: string;
  type: "stack" | "volume" | "image";
}

export interface PatientDetails {
  patientId: string;
  patientName: string;
  files: MedicalFileItem[];
}

// Annotation Types
export interface AnnotationData {
  annotationUID: string;
  toolName: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
  metadata: {
    viewportId?: string;
    frameOfReferenceUID?: string;
    referencedImageId?: string;
  };
}

// Fabric Toolbar Types
export interface FabricToolbarProps {
  // Canvas reference getter function to handle timing issues
  getCanvas: () => Canvas | null;

  // Initial values for Fabric controls
  initialValues: {
    brightness?: number;
    contrast?: number;
    saturation?: number;
    rotation?: number;
  };

  // Save callback
  onSave?: (values: {
    brightness: number;
    contrast: number;
    saturation: number;
    rotation: number;
  }) => void;
}
