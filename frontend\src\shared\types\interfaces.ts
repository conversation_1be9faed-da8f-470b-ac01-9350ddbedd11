import { Canvas } from "fabric";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
}

export interface DicomStackData {
  id: string;
  viewer: {
    imageUrl: string;
    configs: {
      contrast: number;
      brightness: number;
      annotations?: AnnotationData[];
    };
  };
}

export interface DicomVolumeData {
  id: string;
  viewer: {
    imageUrl: string[];
    configs: {
      shift: number;
    };
  };
}

export interface MedicalImageData {
  id: string;
  viewer: {
    imageUrl: string;
    configs: {
      brightness: number;
      contrast: number;
      saturation: number;
      rotation: number;
    };
  };
}

export interface DicomStackViewerProps {
  data: DicomStackData;
}

export interface DicomVolumeViewerProps {
  data: DicomVolumeData;
}

export interface MedicalImageViewerProps {
  data: MedicalImageData;
}

export interface DicomStackConfig {
  contrast: number;
  brightness: number;
  annotations?: AnnotationData[];
}

export interface DicomVolumeConfig {
  shift: number;
}

export interface MedicalImageConfig {
  brightness: number;
  contrast: number;
  saturation: number;
  rotation: number;
}

export interface MedicalFileItem {
  id: string;
  name: string;
  type: "stack" | "volume" | "image";
}

export interface PatientDetails {
  patientId: string;
  patientName: string;
  files: MedicalFileItem[];
}

export interface AnnotationData {
  annotationUID: string;
  toolName: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
  metadata: {
    viewportId?: string;
    frameOfReferenceUID?: string;
    referencedImageId?: string;
  };
}

export interface FabricToolbarProps {
  initialValues: {
    brightness?: number;
    contrast?: number;
    saturation?: number;
    rotation?: number;
  };

  onSave?: (values: {
    brightness: number;
    contrast: number;
    saturation: number;
    rotation: number;
  }) => void;

  cssFilterMode: {
    targetElement: () => HTMLCanvasElement | HTMLImageElement | null;
  };

  enableAnnotations?: boolean;
  getAnnotationTarget?: () => HTMLCanvasElement | HTMLImageElement | null;
}
