import React, { useEffect, useState } from "react";
import { Canvas } from "fabric";
import {
  adjustImageBrightness,
  adjustImageContrast,
  adjustImageSaturation,
  rotateImage,
} from "@/components/fabric-toolbar/config/fabricControls";
import { adjustDicomImageBrightness } from "@/components/fabric-toolbar/core/dicomFabricManager";

export interface FabricToolbarProps {
  // Canvas reference for Fabric.js controls
  fabricCanvas?: Canvas | null;
  
  // Initial values for Fabric controls
  initialValues: {
    brightness?: number;
    contrast?: number;
    saturation?: number;
    rotation?: number;
  };
  
  // Save callback
  onSave?: (values: {
    brightness: number;
    contrast: number;
    saturation: number;
    rotation: number;
  }) => void;
  
  // For DICOM viewers that also use Fabric brightness overlay
  enableFabricBrightness?: boolean;
}

export default function FabricToolbar({
  fabricCanvas,
  initialValues,
  onSave,
  enableFabricBrightness = false,
}: FabricToolbarProps) {
  // State for Fabric.js controls
  const [brightness, setBrightness] = useState(initialValues.brightness || 1);
  const [contrast, setContrast] = useState(initialValues.contrast || 1);
  const [saturation, setSaturation] = useState(initialValues.saturation || 1);
  const [rotation, setRotation] = useState(initialValues.rotation || 0);
  const [fabricBrightness, setFabricBrightness] = useState(0);

  // Update state when initial values change
  useEffect(() => {
    setBrightness(initialValues.brightness || 1);
    setContrast(initialValues.contrast || 1);
    setSaturation(initialValues.saturation || 1);
    setRotation(initialValues.rotation || 0);
  }, [initialValues]);

  // Fabric.js image controls
  const handleBrightnessChange = (value: number) => {
    if (!fabricCanvas) return;
    setBrightness(value);
    adjustImageBrightness(fabricCanvas, value);
  };

  const handleContrastChange = (value: number) => {
    if (!fabricCanvas) return;
    setContrast(value);
    adjustImageContrast(fabricCanvas, value);
  };

  const handleSaturationChange = (value: number) => {
    if (!fabricCanvas) return;
    setSaturation(value);
    adjustImageSaturation(fabricCanvas, value);
  };

  const handleRotationChange = (value: number) => {
    if (!fabricCanvas) return;
    setRotation(value);
    rotateImage(fabricCanvas, value);
  };

  // Fabric brightness overlay for DICOM viewers
  const handleFabricBrightnessChange = (value: number) => {
    setFabricBrightness(value);
    adjustDicomImageBrightness(null, value);
  };

  // Save functionality
  const handleSave = () => {
    if (onSave) {
      onSave({
        brightness,
        contrast,
        saturation,
        rotation,
      });
    }
  };

  return (
    <div className="fabric-toolbar">
      {/* Standard Fabric.js Controls */}
      <div className="control-group">
        <label>Brightness:</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={brightness}
          onChange={(e) => handleBrightnessChange(parseFloat(e.target.value))}
        />
        <span>{brightness.toFixed(1)}</span>
      </div>

      <div className="control-group">
        <label>Contrast:</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={contrast}
          onChange={(e) => handleContrastChange(parseFloat(e.target.value))}
        />
        <span>{contrast.toFixed(1)}</span>
      </div>

      <div className="control-group">
        <label>Saturation:</label>
        <input
          type="range"
          min="0"
          max="2"
          step="0.1"
          value={saturation}
          onChange={(e) => handleSaturationChange(parseFloat(e.target.value))}
        />
        <span>{saturation.toFixed(1)}</span>
      </div>

      <div className="control-group">
        <label>Rotation:</label>
        <input
          type="range"
          min="0"
          max="360"
          step="15"
          value={rotation}
          onChange={(e) => handleRotationChange(parseFloat(e.target.value))}
        />
        <span>{rotation}°</span>
      </div>

      {/* Optional Fabric Brightness Overlay for DICOM */}
      {enableFabricBrightness && (
        <div className="control-group">
          <label>Fabric Brightness:</label>
          <input
            type="range"
            min="-127"
            max="127"
            step="1"
            value={fabricBrightness}
            onChange={(e) => handleFabricBrightnessChange(parseInt(e.target.value))}
          />
          <span>{fabricBrightness}</span>
        </div>
      )}

      {/* Save Button */}
      <button className="save-button" onClick={handleSave}>
        Save Settings
      </button>
    </div>
  );
}
