import { Canvas } from "fabric";

export function createAnnotationCanvas(canvasElement: HTMLCanvasElement): Canvas {
  const canvas = new Canvas(canvasElement, {
    isDrawingMode: false,
    selection: true,
    preserveObjectStacking: true,
  });

  // Make canvas transparent for overlay
  canvas.backgroundColor = 'transparent';
  
  return canvas;
}

export function setupAnnotationOverlay(
  targetElement: HTMLCanvasElement | HTMLImageElement,
  containerId: string
): HTMLCanvasElement {
  const container = document.getElementById(containerId) || targetElement.parentElement;
  if (!container) {
    throw new Error('Container not found for annotation overlay');
  }

  // Create overlay canvas
  const overlayCanvas = document.createElement('canvas');
  overlayCanvas.id = `${containerId}-annotation-overlay`;
  overlayCanvas.style.position = 'absolute';
  overlayCanvas.style.top = '0';
  overlayCanvas.style.left = '0';
  overlayCanvas.style.pointerEvents = 'auto';
  overlayCanvas.style.zIndex = '10';

  // Match target element dimensions
  const rect = targetElement.getBoundingClientRect();
  overlayCanvas.width = rect.width;
  overlayCanvas.height = rect.height;
  overlayCanvas.style.width = `${rect.width}px`;
  overlayCanvas.style.height = `${rect.height}px`;

  // Position relative to container
  if (container.style.position !== 'relative' && container.style.position !== 'absolute') {
    container.style.position = 'relative';
  }

  container.appendChild(overlayCanvas);
  
  return overlayCanvas;
}

export function removeAnnotationOverlay(containerId: string) {
  const overlay = document.getElementById(`${containerId}-annotation-overlay`);
  if (overlay) {
    overlay.remove();
  }
}
