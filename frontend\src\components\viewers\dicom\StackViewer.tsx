import { useRef, useEffect, useState } from "react";
import { Types, cache } from "@cornerstonejs/core";
import { ToolGroupManager, annotation } from "@cornerstonejs/tools";
import {
  initializeCornerstone,
  createRenderingEngine,
  setup2dViewport,
  loadDicomStack,
  adjustBrightness,
  adjustContrast,
  setupViewer,
  stackViewerConfig,
  setPrimaryTool,
  toolConfig,
  getViewportAnnotations,
  restoreViewportAnnotations,
} from "@/lib/dicom";

import FabricToolbar from "@/components/toolbars/FabricToolbar";
import { saveStackConfig } from "@/shared/api";
import { dicomConstants } from "@/shared/constants";
import { DicomStackViewerProps } from "@/shared/types";

const { renderingEngineId, viewportId, toolGroupId } = dicomConstants;

export default function StackViewer({ data }: DicomStackViewerProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const viewportRef = useRef<Types.IStackViewport | null>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const dicomFileRef = useRef<string>("");
  const cornerstoneCanvasRef = useRef<HTMLCanvasElement | null>(null);

  const [isInitialized, setIsInitialized] = useState(false);
  const [contrast, setContrast] = useState(data.viewer.configs.contrast);
  const [brightness, setBrightness] = useState(data.viewer.configs.brightness);

  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);
    return () => {
      annotation.state.removeAllAnnotations();
      ToolGroupManager.destroyToolGroup(toolGroupId);
      renderingEngineRef.current?.destroy();
      cache.purgeCache();

      if (cornerstoneCanvasRef.current) {
        cornerstoneCanvasRef.current.style.filter = "";
        cornerstoneCanvasRef.current.style.transform = "";
      }
    };
  }, []);

  useEffect(() => {
    if (!isInitialized || !elementRef.current) return;

    const initializeViewer = async () => {
      const element = elementRef.current;
      if (!element) return;

      const renderingEngine = createRenderingEngine(renderingEngineId);
      renderingEngineRef.current = renderingEngine;
      const viewport = setup2dViewport(renderingEngine, element, viewportId);
      viewportRef.current = viewport;

      setupViewer(
        toolGroupId,
        viewportId,
        renderingEngineId,
        stackViewerConfig
      );

      const imageUrl = data.viewer.imageUrl;
      dicomFileRef.current = imageUrl;

      await loadDicomStack(viewport, imageUrl);
      adjustContrast(viewport, contrast);
      adjustBrightness(viewport, brightness);

      if (data.viewer.configs.annotations) {
        restoreViewportAnnotations(
          data.viewer.configs.annotations,
          viewportId,
          viewport
        );
      }

      const cornerstoneCanvas = viewport.getCanvas();
      if (cornerstoneCanvas) {
        cornerstoneCanvasRef.current = cornerstoneCanvas;
      }
    };

    initializeViewer();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized, data]);

  const handleContrastChange = (value: number) => {
    if (!viewportRef.current) return;
    setContrast(value);
    adjustContrast(viewportRef.current, value);
  };
  const handleBrigtnessChange = (value: number) => {
    if (!viewportRef.current) return;
    setBrightness(value);
    adjustBrightness(viewportRef.current, value);
  };

  const handleSave = async () => {
    const annotations = getViewportAnnotations(viewportId);
    await saveStackConfig(data.id, {
      contrast,
      brightness,
      annotations,
    });
  };

  const handleToolSelect = (toolName: string) => {
    setPrimaryTool(toolName, viewportId);
  };

  return (
    <div className="stack-viewer">
      <div ref={elementRef} style={{ width: "100%", height: "100%" }} />

      <div className="controls">
        <div className="control-group">
          <label>Tools:</label>
          <div className="tools-grid">
            {toolConfig.map((tool) => {
              const IconComponent = tool.icon;
              return (
                <button
                  key={tool.name}
                  onClick={() => handleToolSelect(tool.name)}
                  className="tool-button"
                  title={tool.displayName}
                >
                  <IconComponent />
                  {tool.displayName}
                </button>
              );
            })}
          </div>
        </div>

        <div className="control-group">
          <label>Contrast:</label>
          <input
            type="range"
            min={-127}
            max={127}
            step={1}
            value={contrast}
            onChange={(e) => handleContrastChange(parseFloat(e.target.value))}
          />
          <span>{contrast.toFixed(1)}</span>
        </div>
        <div className="control-group">
          <label>Brightness:</label>
          <input
            type="range"
            min={-127}
            max={127}
            step={1}
            value={brightness}
            onChange={(e) => handleBrigtnessChange(parseFloat(e.target.value))}
          />
          <span>{brightness.toFixed(1)}</span>
        </div>

        <button className="save-button" onClick={handleSave}>
          Save Settings
        </button>
      </div>

      <FabricToolbar
        initialValues={{
          brightness: 1,
          contrast: 1,
          saturation: 1,
          rotation: 0,
        }}
        cssFilterMode={{
          targetElement: () => cornerstoneCanvasRef.current,
        }}
      />
    </div>
  );
}
