import { useRef, useEffect, useState } from "react";
import { Types, cache } from "@cornerstonejs/core";
import { ToolGroupManager, annotation } from "@cornerstonejs/tools";
import { Canvas } from "fabric";
import {
  initializeCornerstone,
  createRenderingEngine,
  setup2dViewport,
  loadDicomStack,
  adjustBrightness,
  adjustContrast,
  setupViewer,
  stackViewerConfig,
  setPrimaryTool,
  toolConfig,
  getViewportAnnotations,
  restoreViewportAnnotations,
} from "@/lib/dicom";
import {
  createCanvas,
  loadImageToCanvas,
  getCurrentImage,
  setCurrentImage,
} from "@/lib/fabric";
import { FabricToolbar } from "@/components/toolbars/fabric";
import { saveStackConfig } from "@/shared/api";
import { dicomConstants } from "@/shared/constants";
import { DicomStackViewerProps } from "@/shared/types";

const { renderingEngineId, viewportId, toolGroupId } = dicomConstants;

export default function StackViewer({ data }: DicomStackViewerProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const viewportRef = useRef<Types.IStackViewport | null>(null);
  const renderingEngineRef = useRef<Types.IRenderingEngine | null>(null);
  const dicomFileRef = useRef<string>("");
  const fabricCanvasRef = useRef<Canvas | null>(null);
  const fabricCanvasElementRef = useRef<HTMLCanvasElement | null>(null);

  const [isInitialized, setIsInitialized] = useState(false);
  const [contrast, setContrast] = useState(data.viewer.configs.contrast);
  const [brightness, setBrightness] = useState(data.viewer.configs.brightness);

  // Initialize Fabric.js overlay canvas
  const initializeFabricOverlay = async (dicomElement: HTMLDivElement) => {
    // Create a canvas element overlay
    const canvasElement = document.createElement("canvas");
    canvasElement.id = `fabric-overlay-${viewportId}`;
    canvasElement.style.position = "absolute";
    canvasElement.style.top = "0";
    canvasElement.style.left = "0";
    canvasElement.style.pointerEvents = "none"; // Allow DICOM interactions to pass through
    canvasElement.style.zIndex = "10";

    // Set canvas size to match DICOM element
    const rect = dicomElement.getBoundingClientRect();
    canvasElement.width = rect.width;
    canvasElement.height = rect.height;

    // Add canvas to DICOM element
    dicomElement.style.position = "relative";
    dicomElement.appendChild(canvasElement);

    // Create Fabric.js canvas
    const fabricCanvas = createCanvas(canvasElement);
    fabricCanvasRef.current = fabricCanvas;
    fabricCanvasElementRef.current = canvasElement;

    // Load a transparent image to enable Fabric.js effects
    const transparentImageData =
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
    await loadImageToCanvas(fabricCanvas, transparentImageData);

    const currentImg = getCurrentImage();
    if (currentImg) {
      setCurrentImage(currentImg);
      // Make the image cover the entire canvas
      currentImg.set({
        left: fabricCanvas.getWidth() / 2,
        top: fabricCanvas.getHeight() / 2,
        scaleX: fabricCanvas.getWidth(),
        scaleY: fabricCanvas.getHeight(),
        originX: "center",
        originY: "center",
      });
      fabricCanvas.renderAll();
    }
  };

  const getCanvas = () => fabricCanvasRef.current;

  useEffect(() => {
    initializeCornerstone();
    setIsInitialized(true);
    return () => {
      annotation.state.removeAllAnnotations();
      ToolGroupManager.destroyToolGroup(toolGroupId);
      renderingEngineRef.current?.destroy();
      cache.purgeCache();

      // Cleanup Fabric.js canvas
      if (fabricCanvasRef.current) {
        fabricCanvasRef.current.dispose();
        fabricCanvasRef.current = null;
      }
      if (fabricCanvasElementRef.current) {
        fabricCanvasElementRef.current.remove();
        fabricCanvasElementRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (!isInitialized || !elementRef.current) return;

    const initializeViewer = async () => {
      const element = elementRef.current;
      if (!element) return;

      const renderingEngine = createRenderingEngine(renderingEngineId);
      renderingEngineRef.current = renderingEngine;
      const viewport = setup2dViewport(renderingEngine, element, viewportId);
      viewportRef.current = viewport;

      setupViewer(
        toolGroupId,
        viewportId,
        renderingEngineId,
        stackViewerConfig
      );

      const imageUrl = data.viewer.imageUrl;
      dicomFileRef.current = imageUrl;

      await loadDicomStack(viewport, imageUrl);
      adjustContrast(viewport, contrast);
      adjustBrightness(viewport, brightness);

      if (data.viewer.configs.annotations) {
        restoreViewportAnnotations(
          data.viewer.configs.annotations,
          viewportId,
          viewport
        );
      }

      // Create Fabric.js overlay canvas for image effects
      await initializeFabricOverlay(element);
    };

    initializeViewer();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isInitialized, data]);

  const handleContrastChange = (value: number) => {
    if (!viewportRef.current) return;
    setContrast(value);
    adjustContrast(viewportRef.current, value);
  };
  const handleBrigtnessChange = (value: number) => {
    if (!viewportRef.current) return;
    setBrightness(value);
    adjustBrightness(viewportRef.current, value);
  };

  const handleSave = async () => {
    const annotations = getViewportAnnotations(viewportId);
    await saveStackConfig(data.id, {
      contrast,
      brightness,
      annotations,
    });
  };

  const handleToolSelect = (toolName: string) => {
    setPrimaryTool(toolName, viewportId);
  };

  return (
    <div className="stack-viewer">
      <div ref={elementRef} style={{ width: "100%", height: "100%" }} />

      <div className="controls">
        <div className="control-group">
          <label>Tools:</label>
          <div className="tools-grid">
            {toolConfig.map((tool) => {
              const IconComponent = tool.icon;
              return (
                <button
                  key={tool.name}
                  onClick={() => handleToolSelect(tool.name)}
                  className="tool-button"
                  title={tool.displayName}
                >
                  <IconComponent />
                  {tool.displayName}
                </button>
              );
            })}
          </div>
        </div>

        <div className="control-group">
          <label>Contrast:</label>
          <input
            type="range"
            min={-127}
            max={127}
            step={1}
            value={contrast}
            onChange={(e) => handleContrastChange(parseFloat(e.target.value))}
          />
          <span>{contrast.toFixed(1)}</span>
        </div>
        <div className="control-group">
          <label>Brightness:</label>
          <input
            type="range"
            min={-127}
            max={127}
            step={1}
            value={brightness}
            onChange={(e) => handleBrigtnessChange(parseFloat(e.target.value))}
          />
          <span>{brightness.toFixed(1)}</span>
        </div>

        <FabricToolbar
          getCanvas={getCanvas}
          initialValues={{
            brightness: 1,
            contrast: 1,
            saturation: 1,
            rotation: 0,
          }}
        />

        <button className="save-button" onClick={handleSave}>
          Save Settings
        </button>
      </div>
    </div>
  );
}
