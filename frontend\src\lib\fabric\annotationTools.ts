import { <PERSON><PERSON>, Rect, Circle, Ellipse, Line, Polygon, Triangle } from "fabric";

export type AnnotationTool =
  | "rectangle"
  | "circle"
  | "ellipse"
  | "line"
  | "polygon"
  | "triangle";

interface AnnotationState {
  isDrawing: boolean;
  currentTool: AnnotationTool | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  currentShape: any;
  startX: number;
  startY: number;
  points: { x: number; y: number }[];
}

const annotationState: AnnotationState = {
  isDrawing: false,
  currentTool: null,
  currentShape: null,
  startX: 0,
  startY: 0,
  points: [],
};

export function setAnnotationTool(canvas: Canvas, tool: AnnotationTool) {
  if (!canvas) return;

  annotationState.currentTool = tool;
  canvas.isDrawingMode = false;
  canvas.selection = false;

  // Remove all existing event listeners
  canvas.off();

  switch (tool) {
    case "rectangle":
      setupRectangleTool(canvas);
      break;
    case "circle":
      setupCircleTool(canvas);
      break;
    case "ellipse":
      setupEllipseTool(canvas);
      break;
    case "line":
      setupLineTool(canvas);
      break;
    case "polygon":
      setupPolygonTool(canvas);
      break;
    case "triangle":
      setupTriangleTool(canvas);
      break;
  }
}

function setupRectangleTool(canvas: Canvas) {
  let startX: number, startY: number;

  canvas.on("mouse:down", (e) => {
    const pointer = canvas.getPointer(e.e);
    startX = pointer.x;
    startY = pointer.y;

    const rect = new Rect({
      left: startX,
      top: startY,
      width: 0,
      height: 0,
      fill: "transparent",
      stroke: "#ff0000",
      strokeWidth: 2,
    });

    annotationState.currentShape = rect;
    annotationState.isDrawing = true;
    canvas.add(rect);
  });

  canvas.on("mouse:move", (e) => {
    if (!annotationState.isDrawing) return;

    const pointer = canvas.getPointer(e.e);
    const rect = annotationState.currentShape;

    rect.set({
      width: Math.abs(pointer.x - startX),
      height: Math.abs(pointer.y - startY),
      left: Math.min(startX, pointer.x),
      top: Math.min(startY, pointer.y),
    });

    canvas.renderAll();
  });

  canvas.on("mouse:up", () => {
    annotationState.isDrawing = false;
    annotationState.currentShape = null;
  });
}

function setupCircleTool(canvas: Canvas) {
  let startX: number, startY: number;

  canvas.on("mouse:down", (e) => {
    const pointer = canvas.getPointer(e.e);
    startX = pointer.x;
    startY = pointer.y;

    const circle = new Circle({
      left: startX,
      top: startY,
      radius: 0,
      fill: "transparent",
      stroke: "#00ff00",
      strokeWidth: 2,
    });

    annotationState.currentShape = circle;
    annotationState.isDrawing = true;
    canvas.add(circle);
  });

  canvas.on("mouse:move", (e) => {
    if (!annotationState.isDrawing) return;

    const pointer = canvas.getPointer(e.e);
    const circle = annotationState.currentShape;
    const radius = Math.sqrt(
      Math.pow(pointer.x - startX, 2) + Math.pow(pointer.y - startY, 2)
    );

    circle.set({ radius });
    canvas.renderAll();
  });

  canvas.on("mouse:up", () => {
    annotationState.isDrawing = false;
    annotationState.currentShape = null;
  });
}

function setupEllipseTool(canvas: Canvas) {
  let startX: number, startY: number;

  canvas.on("mouse:down", (e) => {
    const pointer = canvas.getPointer(e.e);
    startX = pointer.x;
    startY = pointer.y;

    const ellipse = new Ellipse({
      left: startX,
      top: startY,
      rx: 0,
      ry: 0,
      fill: "transparent",
      stroke: "#0000ff",
      strokeWidth: 2,
    });

    annotationState.currentShape = ellipse;
    annotationState.isDrawing = true;
    canvas.add(ellipse);
  });

  canvas.on("mouse:move", (e) => {
    if (!annotationState.isDrawing) return;

    const pointer = canvas.getPointer(e.e);
    const ellipse = annotationState.currentShape;

    ellipse.set({
      rx: Math.abs(pointer.x - startX) / 2,
      ry: Math.abs(pointer.y - startY) / 2,
      left: Math.min(startX, pointer.x),
      top: Math.min(startY, pointer.y),
    });

    canvas.renderAll();
  });

  canvas.on("mouse:up", () => {
    annotationState.isDrawing = false;
    annotationState.currentShape = null;
  });
}

function setupLineTool(canvas: Canvas) {
  canvas.on("mouse:down", (e) => {
    const pointer = canvas.getPointer(e.e);

    const line = new Line([pointer.x, pointer.y, pointer.x, pointer.y], {
      stroke: "#ffff00",
      strokeWidth: 2,
    });

    annotationState.currentShape = line;
    annotationState.isDrawing = true;
    canvas.add(line);
  });

  canvas.on("mouse:move", (e) => {
    if (!annotationState.isDrawing) return;

    const pointer = canvas.getPointer(e.e);
    const line = annotationState.currentShape;

    line.set({
      x2: pointer.x,
      y2: pointer.y,
    });

    canvas.renderAll();
  });

  canvas.on("mouse:up", () => {
    annotationState.isDrawing = false;
    annotationState.currentShape = null;
  });
}

function setupPolygonTool(canvas: Canvas) {
  canvas.on("mouse:down", (e) => {
    const pointer = canvas.getPointer(e.e);
    annotationState.points.push({ x: pointer.x, y: pointer.y });

    if (annotationState.points.length === 1) {
      annotationState.isDrawing = true;
    }
  });

  canvas.on("mouse:dblclick", () => {
    if (annotationState.points.length >= 3) {
      const polygon = new Polygon(annotationState.points, {
        fill: "transparent",
        stroke: "#ff00ff",
        strokeWidth: 2,
      });

      canvas.add(polygon);
    }

    annotationState.points = [];
    annotationState.isDrawing = false;
  });
}

function setupTriangleTool(canvas: Canvas) {
  let clickCount = 0;
  let points: { x: number; y: number }[] = [];

  canvas.on("mouse:down", (e) => {
    const pointer = canvas.getPointer(e.e);
    points.push({ x: pointer.x, y: pointer.y });
    clickCount++;

    if (clickCount === 3) {
      const triangle = new Triangle({
        left: Math.min(points[0].x, points[1].x, points[2].x),
        top: Math.min(points[0].y, points[1].y, points[2].y),
        width:
          Math.max(points[0].x, points[1].x, points[2].x) -
          Math.min(points[0].x, points[1].x, points[2].x),
        height:
          Math.max(points[0].y, points[1].y, points[2].y) -
          Math.min(points[0].y, points[1].y, points[2].y),
        fill: "transparent",
        stroke: "#ffa500",
        strokeWidth: 2,
      });

      canvas.add(triangle);
      clickCount = 0;
      points = [];
    }
  });
}

export function clearAnnotations(canvas: Canvas) {
  if (!canvas) return;

  const objects = canvas.getObjects();
  objects.forEach((obj) => {
    if (
      obj.type === "rect" ||
      obj.type === "circle" ||
      obj.type === "ellipse" ||
      obj.type === "line" ||
      obj.type === "polygon" ||
      obj.type === "polyline" ||
      obj.type === "triangle"
    ) {
      canvas.remove(obj);
    }
  });

  canvas.renderAll();
}

export function disableAnnotationMode(canvas: Canvas) {
  if (!canvas) return;

  annotationState.currentTool = null;
  annotationState.isDrawing = false;
  annotationState.currentShape = null;
  annotationState.points = [];

  // Remove all event listeners
  canvas.off();

  canvas.selection = true;
}
